import { 
  ExtractedEntity, 
  ExtractedTopic, 
  ExtractedArtifact, 
  IntelligenceExtractionData,
  ProcessingMetadata,
  ContextFolder
} from '../types'

/**
 * Intelligence Service for extracting entities, topics, and artifacts from messages
 * Optimized for baseline hardware (9th gen i7 + RTX 2060)
 */
class IntelligenceService {
  private readonly PROCESSING_VERSION = '1.0'

  /**
   * Extract intelligence data from message content
   */
  async extractIntelligence(content: string, attachments?: any[]): Promise<{
    data: IntelligenceExtractionData
    metadata: ProcessingMetadata
  }> {
    const startTime = Date.now()
    
    try {
      // Run extractions in parallel for better performance
      const [entities, topics, artifacts] = await Promise.all([
        this.extractEntities(content),
        this.extractTopics(content),
        this.extractArtifacts(content, attachments)
      ])

      const processingTime = Date.now() - startTime
      
      const data: IntelligenceExtractionData = {
        entities,
        topics,
        artifacts,
        summary: this.generateSummary(content, entities, topics)
      }

      const metadata: ProcessingMetadata = {
        extraction_time_ms: processingTime,
        model_used: 'lightweight_nlp',
        processing_version: this.PROCESSING_VERSION
      }

      return { data, metadata }

    } catch (error) {
      const processingTime = Date.now() - startTime
      
      return {
        data: {
          entities: [],
          topics: [],
          artifacts: []
        },
        metadata: {
          extraction_time_ms: processingTime,
          model_used: 'lightweight_nlp',
          processing_version: this.PROCESSING_VERSION,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      }
    }
  }

  /**
   * Extract entities using lightweight pattern matching
   */
  private async extractEntities(content: string): Promise<ExtractedEntity[]> {
    const entities: ExtractedEntity[] = []
    
    // Technology patterns
    const techPatterns = [
      /\b(React|Vue|Angular|Node\.js|Python|JavaScript|TypeScript|Java|C\+\+|Rust|Go|Docker|Kubernetes|AWS|Azure|GCP)\b/gi,
      /\b(API|REST|GraphQL|SQL|NoSQL|MongoDB|PostgreSQL|MySQL|Redis|Elasticsearch)\b/gi,
      /\b(AI|ML|LLM|GPT|Claude|Gemini|ChatGPT|OpenAI|Anthropic|Google)\b/gi
    ]

    techPatterns.forEach(pattern => {
      const matches = content.match(pattern) || []
      matches.forEach(match => {
        if (!entities.find(e => e.text.toLowerCase() === match.toLowerCase())) {
          entities.push({
            text: match,
            type: 'technology',
            confidence: 0.8
          })
        }
      })
    })

    // Concept patterns
    const conceptPatterns = [
      /\b(performance|optimization|security|scalability|architecture|design|implementation|deployment|testing|debugging)\b/gi,
      /\b(database|frontend|backend|fullstack|microservices|serverless|cloud|infrastructure)\b/gi
    ]

    conceptPatterns.forEach(pattern => {
      const matches = content.match(pattern) || []
      matches.forEach(match => {
        if (!entities.find(e => e.text.toLowerCase() === match.toLowerCase())) {
          entities.push({
            text: match,
            type: 'concept',
            confidence: 0.7
          })
        }
      })
    })

    // Organization patterns
    const orgPatterns = /\b(Microsoft|Google|Apple|Amazon|Meta|OpenAI|Anthropic|GitHub|GitLab|Stack Overflow)\b/gi
    const orgMatches = content.match(orgPatterns) || []
    orgMatches.forEach(match => {
      if (!entities.find(e => e.text.toLowerCase() === match.toLowerCase())) {
        entities.push({
          text: match,
          type: 'organization',
          confidence: 0.9
        })
      }
    })

    return entities.slice(0, 10) // Limit to top 10 entities
  }

  /**
   * Extract topics using keyword clustering
   */
  private async extractTopics(content: string): Promise<ExtractedTopic[]> {
    const topics: ExtractedTopic[] = []
    
    // Define topic categories with keywords
    const topicCategories = {
      'Development': ['code', 'programming', 'development', 'coding', 'implementation', 'build', 'create'],
      'AI & Machine Learning': ['ai', 'ml', 'llm', 'model', 'training', 'inference', 'neural', 'gpt', 'claude'],
      'Performance': ['performance', 'optimization', 'speed', 'fast', 'slow', 'memory', 'cpu', 'gpu'],
      'Architecture': ['architecture', 'design', 'structure', 'pattern', 'framework', 'system', 'component'],
      'Database': ['database', 'sql', 'query', 'data', 'storage', 'table', 'index', 'schema'],
      'Security': ['security', 'authentication', 'authorization', 'encryption', 'vulnerability', 'secure'],
      'Testing': ['test', 'testing', 'unit', 'integration', 'qa', 'bug', 'debug', 'validation'],
      'Deployment': ['deployment', 'deploy', 'production', 'staging', 'release', 'ci/cd', 'docker']
    }

    const contentLower = content.toLowerCase()
    
    Object.entries(topicCategories).forEach(([topicName, keywords]) => {
      const matchedKeywords = keywords.filter(keyword => 
        contentLower.includes(keyword.toLowerCase())
      )
      
      if (matchedKeywords.length > 0) {
        const relevance = Math.min(matchedKeywords.length / keywords.length, 1)
        topics.push({
          name: topicName,
          relevance,
          keywords: matchedKeywords
        })
      }
    })

    return topics.sort((a, b) => b.relevance - a.relevance).slice(0, 5)
  }

  /**
   * Extract artifacts from content and attachments
   */
  private async extractArtifacts(content: string, attachments?: any[]): Promise<ExtractedArtifact[]> {
    const artifacts: ExtractedArtifact[] = []

    // Code blocks
    const codeBlockPattern = /```[\s\S]*?```/g
    const codeMatches = content.match(codeBlockPattern) || []
    codeMatches.forEach((match, index) => {
      const language = match.match(/```(\w+)/)?.[1] || 'unknown'
      artifacts.push({
        type: 'code',
        title: `Code snippet ${index + 1} (${language})`,
        description: `Code block containing ${language} code`
      })
    })

    // Links
    const linkPattern = /https?:\/\/[^\s]+/g
    const linkMatches = content.match(linkPattern) || []
    linkMatches.forEach((link, index) => {
      artifacts.push({
        type: 'link',
        title: `Link ${index + 1}`,
        description: link
      })
    })

    // File attachments
    if (attachments && attachments.length > 0) {
      attachments.forEach(attachment => {
        artifacts.push({
          type: 'document',
          title: attachment.filename || `Attachment ${artifacts.length + 1}`,
          description: `File attachment: ${attachment.file_type || 'unknown type'}`
        })
      })
    }

    return artifacts
  }

  /**
   * Generate a brief summary of the content
   */
  private generateSummary(content: string, entities: ExtractedEntity[], topics: ExtractedTopic[]): string {
    const contentLength = content.length
    const topEntities = entities.slice(0, 3).map(e => e.text).join(', ')
    const topTopics = topics.slice(0, 2).map(t => t.name).join(', ')
    
    let summary = `Message about ${topTopics || 'general discussion'}`
    if (topEntities) {
      summary += ` involving ${topEntities}`
    }
    summary += ` (${contentLength} characters)`
    
    return summary
  }

  /**
   * Suggest context vaults based on extracted intelligence
   */
  suggestVaults(
    extractionData: IntelligenceExtractionData, 
    availableVaults: ContextFolder[]
  ): { vault: ContextFolder; confidence: number }[] {
    const suggestions: { vault: ContextFolder; confidence: number }[] = []
    
    availableVaults.forEach(vault => {
      let confidence = 0
      
      // Match based on vault name and description
      const vaultText = `${vault.name} ${vault.description}`.toLowerCase()
      
      // Check entity matches
      extractionData.entities.forEach(entity => {
        if (vaultText.includes(entity.text.toLowerCase())) {
          confidence += entity.confidence * 0.3
        }
      })
      
      // Check topic matches
      extractionData.topics.forEach(topic => {
        if (vaultText.includes(topic.name.toLowerCase())) {
          confidence += topic.relevance * 0.4
        }
        
        // Check keyword matches
        topic.keywords.forEach(keyword => {
          if (vaultText.includes(keyword.toLowerCase())) {
            confidence += 0.1
          }
        })
      })
      
      if (confidence > 0.1) {
        suggestions.push({ vault, confidence: Math.min(confidence, 1) })
      }
    })
    
    return suggestions.sort((a, b) => b.confidence - a.confidence).slice(0, 3)
  }

  /**
   * Generate a suggested vault name based on extraction data
   */
  suggestVaultName(extractionData: IntelligenceExtractionData): string {
    const topTopic = extractionData.topics[0]
    const topEntity = extractionData.entities[0]
    
    if (topTopic && topEntity) {
      return `${topTopic.name} - ${topEntity.text}`
    } else if (topTopic) {
      return topTopic.name
    } else if (topEntity) {
      return topEntity.text
    } else {
      return 'New Context'
    }
  }
}

export const intelligenceService = new IntelligenceService()
