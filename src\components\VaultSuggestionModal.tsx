import React, { useState, useEffect } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faBrain, faTimes, faTags, faCube, faFolder, faPlus } from '@fortawesome/free-solid-svg-icons'
import {
  IntelligenceExtractionData,
  ContextFolder,
  VaultAssignment
} from '../types'
import { contextVaultService } from '../services/contextVaultService'
import { intelligenceService } from '../services/intelligenceService'

interface VaultSuggestionModalProps {
  isOpen: boolean
  onClose: () => void
  extractionData: IntelligenceExtractionData
  onVaultSelected: (assignment: VaultAssignment) => void
}

interface VaultSuggestion {
  vault: ContextFolder
  confidence: number
}

export const VaultSuggestionModal: React.FC<VaultSuggestionModalProps> = ({
  isOpen,
  onClose,
  extractionData,
  onVaultSelected
}) => {
  const [availableVaults, setAvailableVaults] = useState<ContextFolder[]>([])
  const [suggestions, setSuggestions] = useState<VaultSuggestion[]>([])
  const [selectedVaultId, setSelectedVaultId] = useState<string | null>(null)
  const [showCreateNew, setShowCreateNew] = useState(false)
  const [newVaultName, setNewVaultName] = useState('')
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (isOpen) {
      loadVaultsAndSuggestions()
    }
  }, [isOpen, extractionData])

  const loadVaultsAndSuggestions = async () => {
    try {
      // Get all available contexts
      const allContexts = contextVaultService.getAllContexts()
      setAvailableVaults(allContexts)

      // Generate suggestions
      const vaultSuggestions = intelligenceService.suggestVaults(extractionData, allContexts)
      setSuggestions(vaultSuggestions)

      // Auto-select best suggestion if confidence is high
      if (vaultSuggestions.length > 0 && vaultSuggestions[0].confidence > 0.7) {
        setSelectedVaultId(vaultSuggestions[0].vault.id)
      }

      // Generate suggested vault name for new vault option
      const suggestedName = intelligenceService.suggestVaultName(extractionData)
      setNewVaultName(suggestedName)

    } catch (error) {
      console.error('Error loading vaults and suggestions:', error)
    }
  }

  const handleVaultSelection = (assignment_method: VaultAssignment['assignment_method']) => {
    if (assignment_method === 'skipped') {
      onVaultSelected({
        vault_id: null,
        assignment_method: 'skipped'
      })
      onClose()
      return
    }

    if (assignment_method === 'created_new') {
      setShowCreateNew(true)
      return
    }

    if (selectedVaultId) {
      const selectedSuggestion = suggestions.find(s => s.vault.id === selectedVaultId)
      onVaultSelected({
        vault_id: selectedVaultId,
        assignment_method: selectedSuggestion ? 'suggested' : 'user_selected',
        suggestion_confidence: selectedSuggestion?.confidence,
        user_feedback: 'accepted'
      })
      onClose()
    }
  }

  const handleCreateNewVault = async () => {
    if (!newVaultName.trim()) return

    try {
      setLoading(true)
      const result = await contextVaultService.createContext({
        name: newVaultName,
        objective: `Context vault for ${extractionData.summary || 'pinned content'}`,
        vaultType: 'Personal'
      })

      if (result.success && result.context) {
        onVaultSelected({
          vault_id: result.context.id,
          assignment_method: 'created_new',
          user_feedback: 'accepted'
        })
        onClose()
      }
    } catch (error) {
      console.error('Error creating new vault:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatConfidence = (confidence: number): string => {
    return `${Math.round(confidence * 100)}% match`
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-gray-900 rounded-lg border border-tertiary/50 w-full max-w-2xl mx-4 max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="p-6 border-b border-tertiary/50">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FontAwesomeIcon icon={faBrain} className="text-primary text-lg" />
              <h2 className="text-lg font-semibold text-white">Add to Context Vault</h2>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
            >
              <FontAwesomeIcon icon={faTimes} className="text-gray-400" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {!showCreateNew ? (
            <>
              {/* Extraction Preview */}
              <div className="mb-6 p-4 bg-gray-800 rounded-lg">
                <h3 className="text-sm font-medium text-white mb-3">Detected Content</h3>
                
                {/* Topics */}
                {extractionData.topics.length > 0 && (
                  <div className="mb-3">
                    <div className="flex items-center gap-2 mb-2">
                      <FontAwesomeIcon icon={faTags} className="text-primary text-xs" />
                      <span className="text-xs text-gray-400">Topics:</span>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {extractionData.topics.slice(0, 3).map((topic, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-primary/20 text-primary text-xs rounded"
                        >
                          {topic.name}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Entities */}
                {extractionData.entities.length > 0 && (
                  <div className="mb-3">
                    <div className="flex items-center gap-2 mb-2">
                      <FontAwesomeIcon icon={faCube} className="text-secondary text-xs" />
                      <span className="text-xs text-gray-400">Key Terms:</span>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {extractionData.entities.slice(0, 5).map((entity, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-secondary/20 text-secondary text-xs rounded"
                        >
                          {entity.text}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Summary */}
                {extractionData.summary && (
                  <div className="text-xs text-gray-300">
                    {extractionData.summary}
                  </div>
                )}
              </div>

              {/* Vault Suggestions */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium text-white">Choose Context Vault</h3>

                {/* Suggested Vaults */}
                {suggestions.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="text-xs text-gray-400 uppercase tracking-wide">Suggested</h4>
                    {suggestions.map((suggestion) => (
                      <label
                        key={suggestion.vault.id}
                        className={`flex items-center gap-3 p-3 rounded-lg border cursor-pointer transition-colors ${
                          selectedVaultId === suggestion.vault.id
                            ? 'border-primary bg-primary/10'
                            : 'border-tertiary/50 hover:border-tertiary'
                        }`}
                      >
                        <input
                          type="radio"
                          name="vault"
                          value={suggestion.vault.id}
                          checked={selectedVaultId === suggestion.vault.id}
                          onChange={(e) => setSelectedVaultId(e.target.value)}
                          className="sr-only"
                        />
                        <FontAwesomeIcon
                          icon={faFolder}
                          className="text-primary"
                          style={{ color: suggestion.vault.color }}
                        />
                        <div className="flex-1">
                          <div className="text-sm font-medium text-white">
                            {suggestion.vault.name}
                          </div>
                          <div className="text-xs text-gray-400">
                            {suggestion.vault.description}
                          </div>
                        </div>
                        <div className="text-xs text-primary">
                          {formatConfidence(suggestion.confidence)}
                        </div>
                      </label>
                    ))}
                  </div>
                )}

                {/* All Vaults */}
                {availableVaults.length > suggestions.length && (
                  <div className="space-y-2">
                    <h4 className="text-xs text-gray-400 uppercase tracking-wide">All Vaults</h4>
                    {availableVaults
                      .filter(vault => !suggestions.find(s => s.vault.id === vault.id))
                      .map((vault) => (
                        <label
                          key={vault.id}
                          className={`flex items-center gap-3 p-3 rounded-lg border cursor-pointer transition-colors ${
                            selectedVaultId === vault.id
                              ? 'border-primary bg-primary/10'
                              : 'border-tertiary/50 hover:border-tertiary'
                          }`}
                        >
                          <input
                            type="radio"
                            name="vault"
                            value={vault.id}
                            checked={selectedVaultId === vault.id}
                            onChange={(e) => setSelectedVaultId(e.target.value)}
                            className="sr-only"
                          />
                          <FontAwesomeIcon
                            icon={faFolder}
                            className="text-primary"
                            style={{ color: vault.color }}
                          />
                          <div className="flex-1">
                            <div className="text-sm font-medium text-white">
                              {vault.name}
                            </div>
                            <div className="text-xs text-gray-400">
                              {vault.description}
                            </div>
                          </div>
                        </label>
                      ))}
                  </div>
                )}
              </div>
            </>
          ) : (
            /* Create New Vault Form */
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-white">Create New Context Vault</h3>
              
              <div>
                <label className="block text-xs text-gray-400 mb-2">Vault Name</label>
                <input
                  type="text"
                  value={newVaultName}
                  onChange={(e) => setNewVaultName(e.target.value)}
                  className="w-full px-3 py-2 bg-gray-800 border border-tertiary/50 rounded-lg text-white text-sm focus:outline-none focus:border-primary"
                  placeholder="Enter vault name..."
                  autoFocus
                />
              </div>

              <div className="text-xs text-gray-400">
                This vault will be created in your Personal vault collection.
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-tertiary/50 flex items-center justify-between">
          <button
            onClick={() => handleVaultSelection('skipped')}
            className="text-sm text-gray-400 hover:text-white transition-colors"
          >
            Skip for Now
          </button>

          <div className="flex items-center gap-3">
            {!showCreateNew ? (
              <>
                <button
                  onClick={() => handleVaultSelection('created_new')}
                  className="flex items-center gap-2 px-4 py-2 text-sm text-gray-300 hover:text-white transition-colors"
                >
                  <FontAwesomeIcon icon={faPlus} className="text-xs" />
                  Create New
                </button>
                <button
                  onClick={() => handleVaultSelection('user_selected')}
                  disabled={!selectedVaultId}
                  className="px-4 py-2 bg-primary text-white text-sm rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Add to Vault
                </button>
              </>
            ) : (
              <>
                <button
                  onClick={() => setShowCreateNew(false)}
                  className="px-4 py-2 text-sm text-gray-300 hover:text-white transition-colors"
                >
                  Back
                </button>
                <button
                  onClick={handleCreateNewVault}
                  disabled={!newVaultName.trim() || loading}
                  className="px-4 py-2 bg-primary text-white text-sm rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? 'Creating...' : 'Create & Add'}
                </button>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
